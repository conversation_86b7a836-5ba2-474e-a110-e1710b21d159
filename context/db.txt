Table shops {
  shop_id bigint [pk, increment]
  shop_name varchar(100) [not null]
  address text [not null]
  currency_code char(3) [default: 'USD']
  created_at timestamp [default: `CURRENT_TIMESTAMP`]
  updated_at timestamp [default: `CURRENT_TIMESTAMP`]
  is_active boolean [default: true]
  
  Note: 'updated_at: ON UPDATE CURRENT_TIMESTAMP'
}

Table categories {
  category_id bigint [pk, increment]
  shop_id bigint [not null]
  category_name varchar(100) [not null]
  parent_category_id bigint
  created_at timestamp [default: `CURRENT_TIMESTAMP`]
  updated_at timestamp [default: `CURRENT_TIMESTAMP`]
  is_active boolean [default: true]
  
  Note: 'updated_at: ON UPDATE CURRENT_TIMESTAMP'
}

Table products {
  product_id bigint [pk, increment]
  shop_id bigint [not null]
  product_name varchar(100) [not null]
  description text
  category_id bigint
  barcode varchar(50) [unique]
  unit varchar(20) [default: 'pcs']
  created_at timestamp [default: `CURRENT_TIMESTAMP`]
  updated_at timestamp [default: `CURRENT_TIMESTAMP`]
  is_active boolean [default: true]
  created_by bigint
  updated_by bigint
  
  Note: 'updated_at: ON UPDATE CURRENT_TIMESTAMP'
}

Table product_variants {
  variant_id bigint [pk, increment]
  product_id bigint [not null]
  shop_id bigint [not null]
  attribute_name varchar(50) [not null]
  attribute_value varchar(50) [not null]
  sku varchar(50) [unique]
  barcode varchar(50) [unique]
  additional_price decimal(10,2) [default: 0.00]
  created_at timestamp [default: `CURRENT_TIMESTAMP`]
}

Table roles {
  role_id bigint [pk, increment]
  role_name varchar(50) [not null, unique]
  description text
  permissions json
}

Table users {
  user_id bigint [pk, increment]
  username varchar(50) [not null, unique]
  email varchar(100) [not null, unique]
  password_hash varchar(255) [not null]
  role_id bigint [not null]
   plan_id bigint [not null]
  created_at timestamp [default: `CURRENT_TIMESTAMP`]
  last_login timestamp
  is_active boolean [default: true]
  mfa_secret varchar(100)
}

Table plans {
  plan_id bigint [pk, increment]
  name varchar(50) [not null, unique] // e.g., Free, Pro, Enterprise
  description text
  price numeric(10, 2) [default: 0.00]
  features json
  created_at timestamp [default: `CURRENT_TIMESTAMP`]
}

Table user_shops {
  user_id bigint [pk]
  shop_id bigint [pk]
}

Table suppliers {
  supplier_id bigint [pk, increment]
  shop_id bigint [not null]
  name varchar(100) [not null]
  contact_person varchar(100)
  email varchar(100)
  phone varchar(20)
  address text
  created_at timestamp [default: `CURRENT_TIMESTAMP`]
  updated_at timestamp [default: `CURRENT_TIMESTAMP`]
  is_active boolean [default: true]
  
  Note: 'updated_at: ON UPDATE CURRENT_TIMESTAMP'
}

Table purchase_orders {
  po_id bigint [pk, increment]
  shop_id bigint [not null]
  supplier_id bigint [not null]
  order_date date [not null]
  expected_delivery date
  status enum('draft','ordered','received','cancelled') [default: 'draft']
  total_amount decimal(12,2)
  created_at timestamp [default: `CURRENT_TIMESTAMP`]
  created_by bigint
}

Table po_items {
  po_item_id bigint [pk, increment]
  po_id bigint [not null]
  product_id bigint [not null]
  variant_id bigint
  quantity int [not null]
  unit_cost decimal(10,2) [not null]
  total_cost decimal(12,2) [note: "STORED GENERATED"]
}

Table inventory {
  inventory_id bigint [pk, increment]
  shop_id bigint [not null]
  product_id bigint [not null]
  variant_id bigint
  quantity int [default: 0, not null]
  reorder_threshold int [default: 5, not null]
  price decimal(10,2) [not null]
  last_updated timestamp [default: `CURRENT_TIMESTAMP`]
  
  Note: 'last_updated: ON UPDATE CURRENT_TIMESTAMP'
}

Table transactions {
  transaction_id bigint [pk, increment]
  inventory_id bigint [not null]
  user_id bigint [not null]
  quantity_change int [not null]
  transaction_type enum('PURCHASE','SALE','ADJUSTMENT','RETURN') [not null]
  transaction_date timestamp [default: `CURRENT_TIMESTAMP`]
  notes text
  related_po bigint
  created_at timestamp [default: `CURRENT_TIMESTAMP`]
}

Table audit_log {
  log_id bigint [pk, increment]
  user_id bigint
  action varchar(50) [not null]
  entity_type varchar(50) [not null]
  entity_id bigint [not null]
  details json
  ip_address varchar(45)
  timestamp timestamp [default: `CURRENT_TIMESTAMP`]
}

// Relationships
Ref: shops.shop_id < categories.shop_id
Ref: categories.category_id < categories.parent_category_id
Ref: shops.shop_id < products.shop_id
Ref: categories.category_id < products.category_id
Ref: products.product_id < product_variants.product_id
Ref: shops.shop_id < product_variants.shop_id
Ref: roles.role_id > users.role_id
Ref: users.user_id > user_shops.user_id
Ref: shops.shop_id > user_shops.shop_id
Ref: shops.shop_id < suppliers.shop_id
Ref: shops.shop_id < purchase_orders.shop_id
Ref: suppliers.supplier_id < purchase_orders.supplier_id
Ref: purchase_orders.po_id < po_items.po_id
Ref: products.product_id < po_items.product_id
Ref: product_variants.variant_id < po_items.variant_id
Ref: shops.shop_id < inventory.shop_id
Ref: products.product_id < inventory.product_id
Ref: product_variants.variant_id < inventory.variant_id
Ref: inventory.inventory_id < transactions.inventory_id
Ref: users.user_id < transactions.user_id
Ref: purchase_orders.po_id < transactions.related_po
Ref: users.user_id < audit_log.user_id
Ref: plans.plan_id < users.plan_id

