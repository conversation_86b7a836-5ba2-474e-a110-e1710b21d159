export interface AuthValidate {
  user_id: number;
  username: string;
  email: string;
  created_at?: Date;
  last_login?: Date;
  is_active?: boolean;
  mfa_secret?: string;
  roles: Array<object>;
  plan: object;
}

export interface AuthUser {
  user_id: number;
  username: string;
  email: string;
  is_active?: boolean;
  roles: Array<string>;
  plan: string;
  refreshToken: string;
  accessToken: string;
}

export interface AuthLogin {
  username: string;
  password: string;
}

export interface AuthRegister {
  username: string;
  password: string;
  email: string;
}
