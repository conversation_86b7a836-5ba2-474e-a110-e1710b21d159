import { Injectable, UnauthorizedException } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import { Response } from 'express';

import * as bcrypt from 'bcrypt';

import { UsersService } from 'src/users/users.service';
import { User } from 'src/users/entities/user.entity';

@Injectable()
export class AuthService {
  constructor(
    private readonly userService: UsersService,
    private readonly jwtService: JwtService,
    private readonly ConfigService: ConfigService,
  ) {}

  async login(user: User, res: Response) {
    const payload = { id: user.user_id, username: user.username };

    const accessToken = await this.jwtService.signAsync(payload, {
      secret: this.ConfigService.get('JWT_ACCESS_TOKEN_SECRET'),
      expiresIn: this.ConfigService.get('JWT_ACCESS_TOKEN_EXPIRATION_TIME'),
    });

    res.cookie('access_token', accessToken, {
      httpOnly: true,
      secure: this.ConfigService.get('NODE_ENV') === 'production',
      expires: new Date(Date.now() + 1000 * 60 * 15),
    });

    return { message: 'Logged in successfully' };
  }

  async validateUser(username: string, pass: string) {
    try {
      const user = await this.userService.findOne({ username: username });
      if (!user) {
        throw new UnauthorizedException('User not found');
      }
      const authenticated = await bcrypt.compare(pass, user.password);
      if (!authenticated) {
        throw new UnauthorizedException();
      }
      return user;
    } catch {
      throw new UnauthorizedException('Credentials are not valid');
    }
  }
}
