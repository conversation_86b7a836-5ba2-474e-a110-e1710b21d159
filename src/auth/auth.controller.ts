import { Controller, Post, HttpCode, HttpStatus } from '@nestjs/common';
import { UseGuards } from '@nestjs/common';
import { Res } from '@nestjs/common';

import { Response } from 'express';

import { AuthService } from './auth.service';
import { LocalAuthGuard } from './guards/local-auth.guard';

import { CurrentUser } from './decorators/curent-user.decorator';
import { User } from '../users/entities/user.entity';

@Controller('auth')
export class AuthController {
  constructor(private readonly authService: AuthService) {}

  @UseGuards(LocalAuthGuard)
  @HttpCode(HttpStatus.OK)
  @Post('login')
  login(@CurrentUser() user: User, @Res({ passthrough: true }) res: Response) {
    return this.authService.login(user, res as any);
  }
}
