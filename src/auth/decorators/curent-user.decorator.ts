import { createParamDecorator, ExecutionContext } from '@nestjs/common';
import { Request } from 'express';

export const getCurrentUser = (context: ExecutionContext) => {
  const request = context.switchToHttp().getRequest<Request>();
  return request.user;
};

export const CurrentUser = createParamDecorator(
  (_data: unknown, context: ExecutionContext) => {
    return getCurrentUser(context);
  },
);
