import { TypeOrmModule } from '@nestjs/typeorm';
import { Module } from '@nestjs/common';

import { UsersModule } from './users/users.module';
import { ShopsModule } from './shops/shops.module';
import { RolesModule } from './roles/roles.module';
import { PlansModule } from './plans/plans.module';
import { AuthModule } from './auth/auth.module';

@Module({
  imports: [
    TypeOrmModule.forRoot({
      type: 'postgres',
      host: 'localhost',
      port: 5432,
      username: 'postgres',
      password: '1111',
      database: 'bay3a',
      entities: [__dirname + '/**/*.entity{.ts,.js}'],
      synchronize: true,
    }),
    UsersModule,
    ShopsModule,
    RolesModule,
    PlansModule,
    AuthModule,
  ],
  controllers: [],
  providers: [],
})
export class AppModule {}
