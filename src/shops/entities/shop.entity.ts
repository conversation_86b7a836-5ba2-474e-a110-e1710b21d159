import {
  Column,
  <PERSON><PERSON><PERSON>,
  JoinTable,
  ManyToMany,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { User } from '../../users/entities/user.entity';

/* Table shops {
  shop_id bigint [pk, increment]
  shop_name varchar(100) [not null]
  address text [not null]
  currency_code char(3) [default: 'USD']
  created_at timestamp [default: `CURRENT_TIMESTAMP`]
  updated_at timestamp [default: `CURRENT_TIMESTAMP`]
  is_active boolean [default: true]
  
  Note: 'updated_at: ON UPDATE CURRENT_TIMESTAMP'
} */

@Entity({ name: 'shops' })
export class Shop {
  @PrimaryGeneratedColumn()
  shop_id: number;

  @Column({ type: 'varchar', length: 255 })
  shop_name: string;

  @Column()
  address: string;

  @Column({ type: 'char', length: 3, default: 'USD' })
  currency_code: string;

  @Column({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  created_at: Date;

  @Column({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  updated_at: Date;

  @Column({ type: 'boolean', default: true })
  is_active: boolean;

  @ManyToMany(() => User)
  @JoinTable()
  users: User[];
}
