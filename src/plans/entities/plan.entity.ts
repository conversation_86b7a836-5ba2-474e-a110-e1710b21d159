import {
  <PERSON>umn,
  CreateDate<PERSON>olumn,
  <PERSON><PERSON>ty,
  OneToMany,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { User } from '../../users/entities/user.entity';

/* Table plans {
  plan_id bigint [pk, increment]
  name varchar(50) [not null, unique] // e.g., Free, Pro, Enterprise
  description text
  price numeric(10, 2) [default: 0.00]
  features json
  created_at timestamp [default: `CURRENT_TIMESTAMP`]
}
 */

@Entity({ name: 'plans' })
export class Plan {
  @PrimaryGeneratedColumn({ type: 'bigint' })
  plan_id: number;

  @Column({ type: 'varchar', length: 50, unique: true })
  plan_name: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({ type: 'numeric', precision: 10, scale: 2, default: 0.0 })
  price: number;

  @Column({ type: 'json', nullable: true })
  features: string;

  @CreateDateColumn({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  created_at: Date;

  @OneToMany(() => User, (user) => user.plan)
  user: User[];
}
