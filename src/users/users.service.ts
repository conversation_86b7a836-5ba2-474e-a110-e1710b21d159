import {
  ConflictException,
  Injectable,
  InternalServerErrorException,
  NotFoundException,
} from '@nestjs/common';
import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';

import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { User } from './entities/user.entity';
import { FindUserOptions } from './types/userTypes';

import * as bcrypt from 'bcrypt';
import { UpdatePasswordDto } from './dto/update-password.dto';

@Injectable()
export class UsersService {
  constructor(
    @InjectRepository(User) private usersRepository: Repository<User>,
  ) {}

  async create(createUserDto: CreateUserDto) {
    const { username, email, password } = createUserDto;

    const existingUser = await this.usersRepository.findOne({
      where: [{ email }, { username }],
    });
    if (existingUser) {
      throw new ConflictException('User already exists');
    }

    let hashedPassword: string;
    try {
      hashedPassword = await bcrypt.hash(password, 10);
    } catch {
      throw new InternalServerErrorException('Failed to hash password');
    }

    const user = this.usersRepository.create({
      username,
      email,
      password: hashedPassword,
    });

    try {
      return await this.usersRepository.save(user);
    } catch {
      throw new InternalServerErrorException('Failed to create user');
    }
  }

  findAll() {
    return this.usersRepository.find();
  }

  async findOne(options: FindUserOptions) {
    const where: Record<string, any> = {};

    if (options.id !== undefined) where.user_id = options.id;
    if (options.email !== undefined) where.email = options.email;
    if (options.username !== undefined) where.username = options.username;

    const user = await this.usersRepository.findOne({ where });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    return user;
  }

  findOneWithRelations(options: FindUserOptions) {
    const where: Record<string, any> = {};

    if (options.id !== undefined) where.user_id = options.id;
    if (options.email !== undefined) where.email = options.email;
    if (options.username !== undefined) where.username = options.username;

    return this.usersRepository.findOne({
      where,
      relations: ['roles', 'plan'],
    });
  }

  async update(id: number, updateUserDto: UpdateUserDto) {
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { password, ...safeUpdateDto } = updateUserDto;
    return this.usersRepository.update({ user_id: id }, safeUpdateDto);
  }

  remove(id: number) {
    return this.usersRepository.delete({ user_id: id });
  }

  async updatePassword(id: number, updatePasswordDto: UpdatePasswordDto) {
    const { password } = updatePasswordDto;

    let hashedPassword: string;
    try {
      hashedPassword = await bcrypt.hash(password, 10);
    } catch {
      throw new InternalServerErrorException('Failed to hash password');
    }

    return this.usersRepository.update(
      { user_id: id },
      { password: hashedPassword },
    );
  }
}
