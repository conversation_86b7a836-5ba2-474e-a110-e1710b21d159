import {
  <PERSON>umn,
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  OneToMany,
  CreateDateColumn,
  ManyToOne,
} from 'typeorm';
import { Role } from '../../roles/entities/role.entity';
import { Plan } from '../../plans/entities/plan.entity';

/* 
Table users {
  user_id bigint [pk, increment]
  username varchar(50) [not null, unique]
  email varchar(100) [not null, unique]
  password_hash varchar(255) [not null]
  role_id bigint [not null]
  created_at timestamp [default: `CURRENT_TIMESTAMP`]
  last_login timestamp
  is_active boolean [default: true]
  mfa_secret varchar(100)
} */

@Entity({ name: 'users' })
export class User {
  @PrimaryGeneratedColumn({ type: 'bigint' })
  user_id: number;

  @Column({ type: 'varchar', length: 255, unique: true })
  username: string;

  @Column({ type: 'varchar', length: 255, unique: true })
  email: string;

  @Column({ type: 'varchar', length: 255 })
  password: string;

  @CreateDateColumn({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  created_at: Date;

  @CreateDateColumn({ type: 'timestamp', nullable: true })
  last_login: Date;

  @Column({ type: 'boolean', default: false })
  is_active: boolean;

  @Column({ type: 'varchar', nullable: true })
  mfa_secret: string;

  @OneToMany(() => Role, (role) => role.user)
  roles: Role[];

  @ManyToOne(() => Plan, (plan) => plan.user)
  plan: Plan;
}
