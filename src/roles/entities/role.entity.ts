import { Column, <PERSON><PERSON><PERSON>, ManyToOne, PrimaryGeneratedColumn } from 'typeorm';
import { User } from '../../users/entities/user.entity';

/* 
Table roles {
  role_id bigint [pk, increment]
  role_name varchar(50) [not null, unique]
  description text
  permissions json
} */

@Entity({ name: 'roles' })
export class Role {
  @PrimaryGeneratedColumn({ type: 'bigint' })
  role_id: number;

  @Column({ type: 'varchar', length: 50, unique: true })
  role_name: string;

  @Column({ type: 'text' })
  description: string;

  @Column({ type: 'json' })
  permissions: string;

  @ManyToOne(() => User, (user) => user.roles)
  user: User[];
}
